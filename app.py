from flask import Flask, render_template, request, redirect, url_for, abort, jsonify, session
import json
from datetime import datetime
import os
from models import db, Post, User
from sqlalchemy import func, text, orm, and_ # 添加 and_ 导入
from sqlalchemy.sql.expression import func as sqlfunc # 区分 func
import random
from functools import wraps
from werkzeug.security import check_password_hash
from markdown_utils import render_markdown, validate_markdown

app = Flask(__name__)
app.secret_key = '9012cb917a02a24923f81ec2e4eeff2192542dba'  # 设置会话密钥

# 配置会话cookie，添加端口相关信息
app.config['SESSION_COOKIE_NAME'] = f'session_{os.environ.get("PORT", "5001")}'
app.config['SESSION_COOKIE_PATH'] = '/'
app.config['SESSION_COOKIE_DOMAIN'] = None

# 配置SQLite数据库
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'weibo.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 设置最大上传大小为 100MB

# 初始化数据库
db.init_app(app)

with app.app_context():
    db.create_all()

# 存储每个 session 的微博 ID 列表和当前索引
# 注意：这在多进程/多线程环境下不是线程安全的，仅适用于开发或单进程部署
# 对于生产环境，需要使用更健壮的存储方式（如 Redis, Memcached, 或数据库）
session_post_ids = {}

def get_random_post_and_index():
    """获取所有微博 ID，随机选择一个并返回微博和其在列表中的索引"""
    global session_post_ids
    user_id = session.get('user_id') # 使用 Flask session ID
    if not user_id:
         # 如果没有用户ID（理论上 login_required 会阻止），返回错误
         print("Error: No user_id in session for get_random_post_and_index")
         return None, -1, []

    if user_id not in session_post_ids:
        print(f"Initializing post IDs for user {user_id}")
        # 优化：只查询 ID
        all_post_ids_tuples = db.session.query(Post.id).order_by(Post.created_at.desc()).all()
        all_post_ids = [pid[0] for pid in all_post_ids_tuples]
        if not all_post_ids:
            print(f"No posts found for user {user_id}")
            return None, -1, []
        session_post_ids[user_id] = {'ids': all_post_ids, 'current_index': -1}
        print(f"Initialized {len(all_post_ids)} post IDs for user {user_id}")

    post_ids = session_post_ids[user_id]['ids']
    if not post_ids:
        print(f"No post IDs list found for user {user_id}")
        return None, -1, []

    random_index = random.randint(0, len(post_ids) - 1)
    session_post_ids[user_id]['current_index'] = random_index
    post_id = post_ids[random_index]
    print(f"Selected random index {random_index} (Post ID: {post_id}) for user {user_id}")
    post = Post.query.get(post_id)
    if not post:
        print(f"Post with ID {post_id} not found in DB. Refreshing list for user {user_id}")
        # 如果帖子在数据库中找不到了（可能被删除），则刷新列表并重试一次
        del session_post_ids[user_id]
        return get_random_post_and_index() # 递归调用以获取新的列表和帖子

    return post, random_index, post_ids

# 身份验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        user = User.query.filter_by(username=username).first()
        if user and check_password_hash(user.password_hash, password):
            session['user_id'] = user.id
            return redirect(url_for('index'))
        else:
            return '用户名或密码错误', 401
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.pop('user_id', None)
    return redirect(url_for('login'))

@app.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    tag = request.args.get('tag')
    per_page = 10  # 每页显示10条微博
    query = Post.query
    if tag == 'no_tags':
        # 筛选没有标签的微博
        query = query.filter(
            db.or_(
                db.text("tags = '[]'"),
                db.text("tags IS NULL")
            )
        )
    elif tag:
        # 使用SQLite的json_each函数来处理JSON数组
        query = query.filter(
            db.text("EXISTS (SELECT 1 FROM json_each(tags) WHERE value = :tag)")
        ).params(tag=tag)
    
    # 支持多关键词搜索
    search_query_string = request.args.get('q')
    if search_query_string:
        keywords = search_query_string.split()  # 按空格分割关键词
        if keywords:  # 确保至少有一个关键词
            conditions = []
            params = {}
            for i, keyword in enumerate(keywords):
                param_name = f"keyword_{i}"
                keyword_like = f'%{keyword}%'
                conditions.append(
                    db.or_(
                        Post.content.ilike(keyword_like),  # ilike 不需要显式参数绑定
                        db.text(f"EXISTS (SELECT 1 FROM json_each(tags) WHERE value LIKE :{param_name})")
                    )
                )
                params[param_name] = keyword_like
            query = query.filter(and_(*conditions)).params(**params)
    
    pagination = query.order_by(Post.created_at.desc()).paginate(page=page, per_page=per_page, error_out=False)
    posts = pagination.items

    # 为每个微博添加渲染后的HTML内容
    for post in posts:
        post.rendered_content = render_markdown(post.content)

    return render_template('index.html', posts=posts, pagination=pagination, current_tag=tag, search_query=search_query_string)

@app.route('/random')
@login_required
def random_post_page():
    """渲染随机微博页面"""
    print(f"Accessing /random for user {session.get('user_id')}")
    post, index, post_ids = get_random_post_and_index()
    if not post:
        print("No post found for /random page")
        return render_template('random.html', post=None, post_id=None, is_first=True, is_last=True)

    is_first = (index == 0)
    is_last = (index == len(post_ids) - 1)

    # 为微博添加渲染后的HTML内容
    post.rendered_content = render_markdown(post.content)

    print(f"Rendering random.html with Post ID: {post.id}, Index: {index}, is_first: {is_first}, is_last: {is_last}")
    return render_template('random.html', post=post, post_id=post.id, is_first=is_first, is_last=is_last)

@app.route('/api/random_post', methods=['GET'])
@login_required
def get_random_post_api():
    """API 端点：获取下一条随机微博"""
    print(f"Accessing /api/random_post for user {session.get('user_id')}")
    post, index, post_ids = get_random_post_and_index()
    if not post:
        print("No post found for /api/random_post")
        return jsonify({'success': False, 'message': '没有找到微博'}), 404

    is_first = (index == 0)
    is_last = (index == len(post_ids) - 1)
    print(f"API /api/random_post returning Post ID: {post.id}, Index: {index}, is_first: {is_first}, is_last: {is_last}")

    return jsonify({
        'success': True,
        'post': {
            'id': post.id,
            'content': post.content,
            'rendered_content': render_markdown(post.content),
            'tags': post.tags or [], # 确保 tags 是列表
            'created_at': post.created_at.strftime('%Y-%m-%d %H:%M'),
        },
        'is_first': is_first,
        'is_last': is_last
    })

@app.route('/api/post/navigate', methods=['GET'])
@login_required
def navigate_post_api():
    """API 端点：根据当前微博 ID 获取上一条或下一条微博"""
    direction = request.args.get('direction') # 'previous' or 'next'
    user_id = session.get('user_id')
    print(f"Accessing /api/post/navigate?direction={direction} for user {user_id}")

    if not user_id or user_id not in session_post_ids:
        print(f"Session data not found for user {user_id}. Reinitializing.")
        # 如果 session 数据丢失，重新生成
        post, index, post_ids = get_random_post_and_index()
        if not post:
             print("Failed to reinitialize and get random post.")
             return jsonify({'success': False, 'message': '无法加载微博数据，请刷新'}), 500
        # 返回刚获取的随机帖子信息
        is_first = (index == 0)
        is_last = (index == len(post_ids) - 1)
        print(f"Reinitialized and returning Post ID: {post.id}")
        return jsonify({
            'success': True,
            'post': {
                'id': post.id,
                'content': post.content,
                'rendered_content': render_markdown(post.content),
                'tags': post.tags or [],
                'created_at': post.created_at.strftime('%Y-%m-%d %H:%M'),
            },
            'is_first': is_first,
            'is_last': is_last
        })

    user_session_data = session_post_ids[user_id]
    post_ids = user_session_data.get('ids', [])
    current_index = user_session_data.get('current_index', -1)
    print(f"Current state for user {user_id}: Index={current_index}, Total IDs={len(post_ids)}")

    if not post_ids or current_index == -1:
        print(f"No valid post list or index for user {user_id}. Re-fetching random.")
        # 如果列表为空或索引无效，尝试重新获取随机帖子
        return get_random_post_api()

    new_index = current_index
    moved = False
    if direction == 'previous':
        if current_index > 0:
            new_index = current_index - 1
            moved = True
            print(f"Navigating previous: New index {new_index}")
        else:
            print("Already at the first post.")
            # 已经是第一条，直接返回当前帖子信息，标记为 is_first
            post_id = post_ids[current_index]
            post = Post.query.get(post_id)
            # 需要处理 post 可能为空的情况
            if not post:
                del session_post_ids[user_id]
                return jsonify({'success': False, 'message': '微博未找到，请刷新'}), 404
            return jsonify({
                'success': True, # 导航本身不算失败，只是没有移动
                'post': {
                    'id': post.id,
                    'content': post.content,
                    'rendered_content': render_markdown(post.content),
                    'tags': post.tags or [],
                    'created_at': post.created_at.strftime('%Y-%m-%d %H:%M')
                },
                'is_first': True,
                'is_last': current_index == len(post_ids) - 1
            })
    elif direction == 'next':
        if current_index < len(post_ids) - 1:
            new_index = current_index + 1
            moved = True
            print(f"Navigating next: New index {new_index}")
        else:
             print("Already at the last post.")
             # 已经是最后一条，直接返回当前帖子信息，标记为 is_last
             post_id = post_ids[current_index]
             post = Post.query.get(post_id)
             if not post:
                 del session_post_ids[user_id]
                 return jsonify({'success': False, 'message': '微博未找到，请刷新'}), 404
             return jsonify({
                 'success': True,
                 'post': {
                     'id': post.id,
                     'content': post.content,
                     'rendered_content': render_markdown(post.content),
                     'tags': post.tags or [],
                     'created_at': post.created_at.strftime('%Y-%m-%d %H:%M')
                 },
                 'is_first': current_index == 0,
                 'is_last': True
             })
    else:
        print(f"Invalid direction: {direction}")
        return jsonify({'success': False, 'message': '无效的导航方向'}), 400

    # 只有在成功移动后才更新索引和获取新帖子
    if moved:
        user_session_data['current_index'] = new_index
        post_id = post_ids[new_index]
        post = Post.query.get(post_id)
        print(f"Fetching post with ID {post_id} at new index {new_index}")

        if not post:
            print(f"Post with ID {post_id} not found. Refreshing list for user {user_id}.")
            # 如果数据库中的帖子被删除等情况
            del session_post_ids[user_id] # 清理无效数据
            return jsonify({'success': False, 'message': '微博未找到，请刷新'}), 404

        is_first = (new_index == 0)
        is_last = (new_index == len(post_ids) - 1)
        print(f"Returning navigated post: ID={post.id}, is_first={is_first}, is_last={is_last}")

        return jsonify({
            'success': True,
            'post': {
                'id': post.id,
                'content': post.content,
                'rendered_content': render_markdown(post.content),
                'tags': post.tags or [],
                'created_at': post.created_at.strftime('%Y-%m-%d %H:%M'),
            },
            'is_first': is_first,
            'is_last': is_last
        })
    else:
         # 如果没有移动（例如在边界），理论上应该已经被上面的 return 处理了
         # 但为了保险起见，添加一个错误返回
         print("Navigation logic error: No move detected but didn't return earlier.")
         return jsonify({'success': False, 'message': '导航逻辑错误'}), 500

@app.route('/post/create', methods=['POST'])
@login_required
def create_post():
    content = request.form.get('content')
    tags_json = request.form.get('tags', '[]')
    if not content:
        return jsonify({'success': False, 'message': '内容不能为空'}), 400

    # 验证Markdown内容
    validation_result = validate_markdown(content)
    if not validation_result['valid']:
        return jsonify({'success': False, 'message': validation_result['errors'][0]}), 400

    try:
        tags = json.loads(tags_json)
        post = Post(content=content, tags=tags)
        db.session.add(post)
        db.session.commit()

        # 返回新创建的微博数据，用于前端异步更新
        return jsonify({
            'success': True,
            'post': {
                'id': post.id,
                'content': post.content,
                'rendered_content': render_markdown(post.content),
                'tags': post.tags,
                'created_at': post.created_at.strftime('%Y-%m-%d %H:%M'),
                'current_tag': request.args.get('tag')
            }
        })
    except json.JSONDecodeError:
        return jsonify({'success': False, 'message': '标签格式无效'}), 400
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500
    return redirect(url_for('index'))

@app.route('/api/post/<int:post_id>', methods=['GET'])
@login_required
def get_post_api(post_id):
    """API端点：获取单个微博的详细信息"""
    post = Post.query.get_or_404(post_id)
    return jsonify({
        'success': True,
        'post': {
            'id': post.id,
            'content': post.content,  # 原始Markdown内容
            'rendered_content': render_markdown(post.content),
            'tags': post.tags or [],
            'created_at': post.created_at.strftime('%Y-%m-%d %H:%M')
        }
    })

@app.route('/post/<int:post_id>/update', methods=['POST'])
@login_required
def update_post(post_id):
    post = Post.query.get_or_404(post_id)
    content = request.form.get('content')
    tags = request.form.get('tags')
    if not content:
        return jsonify({'success': False, 'message': '内容不能为空'}), 400

    # 验证Markdown内容
    validation_result = validate_markdown(content)
    if not validation_result['valid']:
        return jsonify({'success': False, 'message': validation_result['errors'][0]}), 400

    post.content = content
    if tags:
        post.tags = json.loads(tags)

    created_at = request.form.get('created_at')
    if created_at:
        try:
            # 直接解析ISO格式的时间字符串，将其视为本地时间（已包含UTC+8时区）
            # 不需要额外加减时区偏移量
            post.created_at = datetime.strptime(created_at, '%Y-%m-%dT%H:%M')
        except ValueError:
            return jsonify({'success': False, 'message': '无效的时间格式'}), 400
    db.session.commit()
    return jsonify({
        'success': True,
        'post': {
            'id': post.id,
            'content': post.content,
            'rendered_content': render_markdown(post.content),
            'tags': post.tags,
            'created_at': post.created_at.strftime('%Y-%m-%d %H:%M'),
            'current_tag': request.args.get('tag')
        }
    })

@app.route('/post/<int:post_id>/delete', methods=['POST'])
@login_required
def delete_post(post_id):
    post = Post.query.get_or_404(post_id)
    db.session.delete(post)
    db.session.commit()
    return redirect(url_for('index'))

# 新增：文件上传接口
@app.route('/upload', methods=['POST'])
# @login_required
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': '未找到文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400

    # 检查文件大小是否超过限制 (100MB)
    max_size = 100 * 1024 * 1024  # 100MB
    if len(file.read()) > max_size:
        return jsonify({'error': '文件大小超过限制'}), 413
    file.seek(0)  # 重置文件指针以便保存文件

    # 保存文件到 uploads 目录
    upload_folder = os.path.join(app.root_path, 'uploads')
    os.makedirs(upload_folder, exist_ok=True)
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
    safe_filename = timestamp + file.filename
    file_path = os.path.join(upload_folder, safe_filename)
    file.save(file_path)

    return jsonify({
        'message': 'upload success',
        'file_path': file_path
    }), 200

@app.route('/api/markdown/preview', methods=['POST'])
@login_required
def markdown_preview():
    """API端点：Markdown预览"""
    content = request.form.get('content', '')
    try:
        rendered_content = render_markdown(content)
        return jsonify({
            'success': True,
            'rendered_content': rendered_content
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/tags')
@login_required
def tags():
    # 使用原生 SQL 查询来处理 JSON 数组和标签统计
    tag_counts = db.session.execute(text("""
        WITH tag_list AS (
            SELECT json_each.value as tag
            FROM post, json_each(post.tags)
        )
        SELECT tag, COUNT(*) as count
        FROM tag_list
        GROUP BY tag
        ORDER BY count DESC
    """)).fetchall()

    # 统计无标签微博的数量
    no_tags_count = Post.query.filter(
        db.or_(
            db.text("tags = '[]'"),
            db.text("tags IS NULL")
        )
    ).count()

    return render_template('tags.html', tag_counts=tag_counts, no_tags_count=no_tags_count)

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5001))  # 默认使用5001端口
    app.run(debug=True, host='127.0.0.1', port=port)