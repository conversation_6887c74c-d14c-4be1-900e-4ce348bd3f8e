# 发布微博模态框组件

这是一个可复用的发布微博模态框组件，包含完整的HTML、CSS和JavaScript代码，可以在任何页面中独立使用，无需引入额外的样式文件或脚本文件。

## 🚀 快速开始

### 1. 基本使用

在你的HTML模板中包含模态框组件：

```html
<!-- 在你的页面中引入发布微博模态框 -->
{% include '_post_modal.html' %}
```

就是这样！模态框会自动初始化，包含一个发布按钮和完整的模态框功能。

### 2. 基本功能

- ✅ 文本输入和编辑
- ✅ 标签添加和管理
- ✅ 响应式设计
- ✅ 表单验证
- ✅ AJAX提交
- ✅ 错误处理
- ✅ 动画效果

## ⚙️ 自定义配置

### 自定义发布成功后的行为

默认情况下，发布成功后会刷新页面。你可以通过定义 `window.onPostCreated` 函数来自定义行为：

```javascript
// 在页面中添加这个脚本
window.onPostCreated = function(post) {
    console.log('新微博发布成功:', post);
    
    // 自定义逻辑
    // 例如：显示通知
    alert('微博发布成功！');
    
    // 例如：动态更新页面内容
    updatePostList(post);
    
    // 例如：跳转到其他页面
    // window.location.href = '/posts/' + post.id;
};
```

### 自定义API端点

如果你的发布微博API端点不是默认的 `/post/create`，可以这样配置：

```javascript
window.API_URLS = {
    createPost: '/your/custom/create/endpoint'
};
```

### 获取模态框控制器实例

模态框初始化后，控制器实例会存储在 `window.postModalController` 中，你可以通过它来程序化控制模态框：

```javascript
// 显示模态框
window.postModalController.showModal();

// 隐藏模态框
window.postModalController.hideModal();

// 获取标签管理器
const tagManager = window.postModalController.tagManager;

// 设置标签
tagManager.setTags(['标签1', '标签2']);

// 获取当前标签
const currentTags = tagManager.getTags();
```

## 🎨 样式自定义

模态框包含了完整的CSS样式，使用CSS变量定义颜色和间距，便于自定义：

```css
/* 覆盖主色调 */
.modal-content {
    --primary-color: #your-color;
    --primary-hover: #your-hover-color;
}

/* 自定义模态框大小 */
.modal-content {
    max-width: 600px; /* 默认是500px */
}

/* 自定义按钮样式 */
.post-button {
    background: linear-gradient(135deg, #your-color1 0%, #your-color2 100%);
}
```

## 📱 响应式设计

模态框自动适应不同屏幕尺寸：

- **桌面端**: 最大宽度500px，居中显示
- **平板端**: 宽度90%，保持良好的视觉效果
- **手机端**: 宽度95%，优化触摸操作

## 🔧 API要求

后端API需要满足以下要求：

### 请求格式
- **方法**: POST  
- **Content-Type**: application/x-www-form-urlencoded
- **参数**:
  - `content`: 微博内容（必填）
  - `tags`: JSON格式的标签数组（可选）

### 响应格式

成功响应：
```json
{
    "success": true,
    "post": {
        "id": 123,
        "content": "微博内容",
        "tags": ["标签1", "标签2"],
        "created_at": "2024-01-01 12:00"
    }
}
```

错误响应：
```json
{
    "success": false,
    "message": "错误信息"
}
```

## 📋 完整示例

```html
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的页面</title>
</head>
<body>
    <div class="container">
        <h1>我的页面</h1>
        <p>这里是页面内容...</p>
    </div>
    
    <!-- 引入发布微博模态框 -->
    {% include '_post_modal.html' %}
    
    <script>
        // 自定义配置
        window.API_URLS = {
            createPost: '{{ url_for("create_post") }}'
        };
        
        // 自定义发布成功后的行为
        window.onPostCreated = function(post) {
            // 显示成功消息
            showNotification('微博发布成功！');
            
            // 更新页面内容
            addPostToPage(post);
        };
        
        function showNotification(message) {
            // 你的通知逻辑
            alert(message);
        }
        
        function addPostToPage(post) {
            // 你的页面更新逻辑
            console.log('新增微博:', post);
        }
    </script>
</body>
</html>
```

## 🔍 调试

如果遇到问题，可以检查：

1. **浏览器控制台**: 查看是否有JavaScript错误
2. **网络请求**: 检查API请求是否正确发送
3. **元素检查**: 确认必要的DOM元素是否存在

```javascript
// 检查模态框是否正确初始化
console.log('模态框控制器:', window.postModalController);

// 检查API配置
console.log('API配置:', window.API_URLS);
```

## 🎯 最佳实践

1. **在模板中使用**: 推荐在Flask模板中使用 `{% include '_post_modal.html' %}` 
2. **自定义回调**: 总是定义 `window.onPostCreated` 函数来处理发布成功后的逻辑
3. **错误处理**: 确保后端API返回正确的JSON格式
4. **样式隔离**: 如果需要多个模态框，注意CSS类名的命名空间

## 📦 文件结构

```
templates/
├── _post_modal.html          # 主模态框组件
├── example_page.html         # 使用示例
└── README_post_modal.md      # 本文档
```

## 🤝 贡献

如果你发现bug或有改进建议，欢迎提交Issue或Pull Request。

---

*这个组件是从原始的 `index.html` 中提取出来的，保持了所有原有功能的同时，提供了更好的复用性和可维护性。* 