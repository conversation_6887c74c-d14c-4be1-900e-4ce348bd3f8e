<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑模态框测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="static/css/tailwind-custom.css">
    <style>
        /* 测试用的基础样式 */
        .modal {
            display: none;
        }
        .modal.show {
            display: block;
        }
        
        /* 动画样式 */
        .animate-fade-in {
            animation: fade-in 0.3s ease-out;
        }
        .animate-slide-in {
            animation: slide-in 0.3s ease-out;
        }
        
        @keyframes fade-in {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slide-in {
            from { 
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-4">编辑模态框测试</h1>
        
        <div class="mb-4 space-x-2">
            <button onclick="showModal()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                打开编辑模态框
            </button>
            <button onclick="resizeWindow(800, 600)" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                正常窗口 (800x600)
            </button>
            <button onclick="resizeWindow(400, 500)" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                低高度窗口 (400x500)
            </button>
            <button onclick="resizeWindow(400, 400)" class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
                极低高度窗口 (400x400)
            </button>
            <button onclick="resizeWindow(400, 350)" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                超低高度窗口 (400x350)
            </button>
        </div>
        
        <div class="bg-white p-4 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-2">测试说明</h2>
            <ul class="list-disc list-inside space-y-1 text-sm">
                <li>点击"打开编辑模态框"按钮打开模态框</li>
                <li>使用不同的窗口尺寸按钮测试不同高度下的显示效果</li>
                <li>检查保存按钮是否始终可见</li>
                <li>检查内容区域是否能正确滚动</li>
                <li>检查在极低窗口高度下的布局是否合理</li>
            </ul>
        </div>
    </div>

    <!-- 编辑微博对话框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="editModal">
        <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] max-w-2xl shadow-2xl animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200 flex-shrink-0">
                <div class="flex items-center gap-2">
                    <input type="datetime-local" id="editPostTime" name="editPostTime" form="editForm"
                           class="px-2 py-1 text-xs border-2 border-gray-200 rounded-xl transition-all duration-300 bg-white text-gray-800 font-inherit focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100">
                    <button type="button" id="setCurrentTimeBtn" class="px-2 py-1 text-xs font-semibold bg-indigo-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 whitespace-nowrap shadow-sm hover:bg-indigo-700 hover:-translate-y-0.5 hover:shadow-md">Now</button>
                </div>
                <button type="button" onclick="hideModal()" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500">&times;</button>
            </div>
            <div class="p-8 flex-1 overflow-y-auto">
                <form id="editForm" class="space-y-6">
                    <div class="flex flex-col gap-2">
                        <!-- Markdown编辑器工具栏 -->
                        <div class="markdown-toolbar">
                            <button type="button" class="toolbar-btn" data-action="bold" title="粗体">
                                <strong>B</strong>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="italic" title="斜体">
                                <em>I</em>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="heading" title="标题">
                                H
                            </button>
                            <button type="button" class="toolbar-btn" data-action="link" title="链接">
                                🔗
                            </button>
                            <button type="button" class="toolbar-btn" data-action="code" title="代码">
                                &lt;/&gt;
                            </button>
                            <button type="button" class="toolbar-btn" data-action="list" title="列表">
                                ≡
                            </button>
                            <div class="toolbar-divider"></div>
                            <button type="button" class="toolbar-btn preview-btn" data-action="preview" title="预览">
                                👁
                            </button>
                        </div>

                        <!-- 编辑器容器 -->
                        <div class="editor-container">
                            <textarea name="content" id="editMarkdownEditor" placeholder="支持Markdown格式，编辑微博内容" required
                                      class="w-full min-h-250 md:min-h-300 p-6 text-base border-none font-mono resize-y bg-white focus:outline-none">这是一个测试内容。

# 标题1
这是一些测试文本，用来测试编辑器的滚动功能。

## 标题2
更多的测试内容，确保有足够的内容来测试滚动。

### 标题3
继续添加更多内容来测试滚动效果。

- 列表项1
- 列表项2
- 列表项3

这里有更多的文本内容，用来确保编辑器有足够的内容来测试滚动功能。当窗口高度很低时，这些内容应该能够正确滚动，而保存按钮应该始终可见。

```javascript
// 代码块示例
function test() {
    console.log("测试代码");
}
```

更多的测试内容...</textarea>
                        </div>
                    </div>
                    <!-- 标签编辑区域 -->
                    <div class="flex items-center justify-between gap-4 md:flex-col md:items-stretch md:gap-4">
                        <div class="flex-1 min-w-0">
                            <label for="editTagInput" class="block text-sm font-medium text-gray-700 mb-1.5">
                                编辑标签 <span class="text-gray-400 text-xs">(输入标签名称后按 Enter 键添加，点击标签上的 × 可删除)</span>
                            </label>
                            <div class="group relative flex items-center flex-wrap gap-2 p-3 border-2 border-gray-200 rounded-xl bg-white min-h-[56px] transition-all duration-300 focus-within:border-indigo-500 focus-within:ring-4 focus-within:ring-indigo-100 focus-within:shadow-sm md:min-h-[50px] md:p-2.5">
                                <!-- 标签容器 -->
                                <div class="flex flex-wrap gap-2" id="editTagsContainer">
                                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-lg">
                                        测试标签1
                                        <button type="button" class="text-indigo-600 hover:text-indigo-800">×</button>
                                    </span>
                                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-lg">
                                        测试标签2
                                        <button type="button" class="text-indigo-600 hover:text-indigo-800">×</button>
                                    </span>
                                </div>

                                <!-- 标签输入框 -->
                                <input type="text"
                                       id="editTagInput"
                                       placeholder="添加标签..."
                                       class="flex-1 min-w-[120px] border-0 outline-0 p-1 text-sm bg-transparent text-gray-700 placeholder-gray-400 md:min-w-[100px]" />
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4 flex-shrink-0">
                <button type="submit" form="editForm" class="px-6 py-2 text-sm font-semibold bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 shadow-sm hover:-translate-y-1 hover:shadow-md">保存修改</button>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            document.getElementById('editModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }
        
        function hideModal() {
            document.getElementById('editModal').classList.remove('show');
            document.body.style.overflow = '';
        }
        
        function resizeWindow(width, height) {
            // 注意：在实际浏览器中，只能调整窗口大小，不能设置具体尺寸
            // 这里只是为了演示，实际测试时需要手动调整浏览器窗口大小
            alert(`请手动将浏览器窗口调整为 ${width}x${height} 像素来测试效果`);
        }
        
        // 点击模态框背景关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
    </script>
</body>
</html>
