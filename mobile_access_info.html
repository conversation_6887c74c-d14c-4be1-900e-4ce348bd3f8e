<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端访问信息</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
        }
        
        .info-content {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .ip-address {
            font-family: 'Monaco', 'Menlo', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 18px;
            text-align: center;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .test-links {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
        }
        
        .test-link {
            display: block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            border-radius: 12px;
            text-align: center;
            font-weight: bold;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .test-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .qr-placeholder {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
        }
        
        .steps {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .step {
            margin-bottom: 15px;
            padding-left: 25px;
            position: relative;
        }
        
        .step::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background: rgba(255, 255, 255, 0.3);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .steps {
            counter-reset: step-counter;
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 移动端测试访问</h1>
        
        <div class="info-card">
            <div class="info-title">🌐 服务器信息</div>
            <div class="info-content">
                <strong>本机IP地址：</strong>
                <div class="ip-address">************</div>
                <strong>端口：</strong> 8080<br>
                <strong>状态：</strong> <span style="color: #4CAF50;">● 运行中</span>
            </div>
        </div>
        
        <div class="info-card">
            <div class="info-title">📋 访问步骤</div>
            <div class="steps">
                <div class="step">确保移动设备与电脑连接到同一个WiFi网络</div>
                <div class="step">在移动设备浏览器中输入下面的地址</div>
                <div class="step">测试编辑对话框的移动端效果</div>
                <div class="step">验证内边距优化是否生效</div>
            </div>
        </div>
        
        <div class="info-card">
            <div class="info-title">🔗 测试链接</div>
            <div class="test-links">
                <a href="http://************:8080/mobile_test.html" class="test-link">
                    📱 移动端对话框测试
                </a>
                <a href="http://************:8080/" class="test-link">
                    📁 文件目录浏览
                </a>
            </div>
        </div>
        
        <div class="qr-placeholder">
            <strong>💡 提示：</strong><br>
            您可以使用手机扫码工具生成二维码，或者直接在移动设备浏览器中输入：<br>
            <code>http://************:8080/mobile_test.html</code>
        </div>
        
        <div class="warning">
            <strong>⚠️ 注意事项：</strong><br>
            • 确保防火墙允许8080端口访问<br>
            • 移动设备必须与电脑在同一局域网内<br>
            • 如果无法访问，请检查网络设置
        </div>
        
        <div class="success">
            <strong>✅ 测试要点：</strong><br>
            • 对话框是否能完整显示<br>
            • 内边距是否合适紧凑<br>
            • 滚动功能是否正常<br>
            • 保存按钮是否可见
        </div>
        
        <div class="info-card">
            <div class="info-title">🛠️ 故障排除</div>
            <div class="info-content">
                <strong>如果无法访问，请尝试：</strong><br>
                1. 检查电脑和手机是否在同一WiFi网络<br>
                2. 确认IP地址是否正确（可能会变化）<br>
                3. 检查防火墙设置<br>
                4. 尝试重启服务器<br>
                5. 使用其他端口（如8081、3000等）
            </div>
        </div>
    </div>
    
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            console.log('页面加载时间:', timeStr);
        }
        
        // 检测是否为移动设备
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            
            if (isMobile()) {
                // 如果是移动设备访问，显示成功提示
                const successDiv = document.querySelector('.success');
                successDiv.innerHTML = '<strong>🎉 太棒了！</strong><br>您已经成功通过移动设备访问了本地服务器！<br>现在可以测试编辑对话框的移动端效果了。';
                successDiv.style.background = 'rgba(40, 167, 69, 0.3)';
            }
        });
    </script>
</body>
</html>
