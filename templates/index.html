<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的微博</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- 引入 Tailwind CSS -->
    <link href="/static/css/tailwind/tailwind.min.css" rel="stylesheet">
    <!-- 引入自定义补充样式 -->
    <link rel="stylesheet" href="/static/css/tailwind-custom.css">

    <!-- Markdown内容样式 -->
    <style>
        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin: 16px 0 8px 0;
            font-weight: 600;
            color: #1f2937;
        }

        .markdown-content h1 { font-size: 1.5em; }
        .markdown-content h2 { font-size: 1.3em; }
        .markdown-content h3 { font-size: 1.1em; }

        .markdown-content p {
            margin: 8px 0;
        }

        .markdown-content strong {
            font-weight: 600;
            color: #1f2937;
        }

        .markdown-content em {
            font-style: italic;
        }

        .markdown-content code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e11d48;
        }

        .markdown-content pre {
            background: #f8fafc;
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 12px 0;
            border: 1px solid #e2e8f0;
        }

        .markdown-content pre code {
            background: none;
            padding: 0;
            color: #374151;
        }

        .markdown-content ul, .markdown-content ol {
            margin: 8px 0;
            padding-left: 24px;
        }

        .markdown-content ul {
            list-style-type: disc;
        }

        .markdown-content ol {
            list-style-type: decimal;
        }

        .markdown-content li {
            margin: 4px 0;
        }

        .markdown-content a {
            color: #6366f1;
            text-decoration: underline;
        }

        .markdown-content a:hover {
            color: #4f46e5;
        }

        .markdown-content blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 16px;
            margin: 12px 0;
            color: #6b7280;
            font-style: italic;
        }

        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 12px 0;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #e5e7eb;
            padding: 8px 12px;
            text-align: left;
        }

        .markdown-content th {
            background: #f8fafc;
            font-weight: 600;
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    {% set active_page = 'index' %}
    {% include '_navbar.html' %}

    <!-- 主容器 - 使用 Tailwind CSS -->
    <div class="container max-w-4xl mx-auto px-8 pt-20 md:px-4 md:pt-16">

        {% if current_tag %}
        <div class="bg-gradient-to-br from-indigo-50 to-purple-50 px-6 py-4 rounded-xl mb-6 font-medium text-gray-800 border border-indigo-200">
            当前筛选：<span class="text-indigo-600 font-semibold">{% if current_tag == 'no_tags' %}无标签{% else %}{{ current_tag }}{% endif %}</span>
            <a href="{{ url_for('index') }}" class="text-gray-600 no-underline ml-2 font-medium hover:text-indigo-600">[显示全部]</a>
        </div>
        {% endif %}

        <!-- 发布微博对话框 -->
        <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="postModal">
            <div class="bg-white my-[5%] mx-auto rounded-2xl w-[90%] max-w-2xl shadow-2xl animate-slide-in overflow-hidden">
                <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200">
                    <h5 class="text-lg font-semibold text-gray-800 m-0">发布微博</h5>
                    <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" data-dismiss="modal">&times;</button>
                </div>
                <div class="p-8">
                    <form id="postForm" class="space-y-6">
                        <div class="flex flex-col gap-2">
                            <textarea name="content" placeholder="有什么新鲜事想分享？" required
                                      class="w-full min-h-250 md:min-h-300 p-6 text-base border-2 border-gray-200 rounded-xl transition-all duration-300 font-inherit resize-y bg-gray-50 focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 focus:bg-white"></textarea>
                        </div>
                        <!-- 标签输入区域 -->
                        <div class="flex items-center justify-between gap-4 md:flex-col md:items-stretch md:gap-4">
                            <div class="flex-1 min-w-0">
                                <label for="tagInput" class="block text-sm font-medium text-gray-700 mb-1.5">
                                    标签 <span class="text-gray-400 text-xs">(按 Enter 添加)</span>
                                </label>
                                <div class="group relative flex items-center flex-wrap gap-2 p-3 border-2 border-gray-200 rounded-xl bg-white min-h-[56px] transition-all duration-300 focus-within:border-indigo-500 focus-within:ring-4 focus-within:ring-indigo-100 focus-within:shadow-sm md:min-h-[50px] md:p-2.5">
                                    <!-- 标签容器 -->
                                    <div class="flex flex-wrap gap-2" id="tagsContainer"></div>

                                    <!-- 标签输入框 -->
                                    <input type="text"
                                           id="tagInput"
                                           placeholder="添加标签..."
                                           class="flex-1 min-w-[120px] border-0 outline-0 p-1 text-sm bg-transparent text-gray-700 placeholder-gray-400 md:min-w-[100px]" />

                                    <!-- 输入提示图标 -->
                                    <div class="flex-shrink-0 opacity-40 group-focus-within:opacity-60 transition-opacity duration-300">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <input type="hidden" name="tags" id="tagsField" value="[]" />

                                <!-- 标签输入提示 -->
                                <p class="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    输入标签名称后按 Enter 键添加，点击标签上的 × 可删除
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4">
                    <button type="submit" form="postForm" class="px-6 py-2 text-sm font-semibold bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 shadow-sm hover:-translate-y-1 hover:shadow-md">发布</button>
                </div>
            </div>
        </div>

        <!-- 编辑微博对话框 -->
        {% include '_edit_modal.html' %}

        <!-- 微博列表 -->
        <div class="posts-list space-y-8 md:space-y-6">
            {% for post in posts %}
            <div class="card bg-white rounded-2xl p-8 shadow-sm border border-gray-200 relative overflow-hidden md:p-6" data-post-id="{{ post.id }}">
                <div class="card-header mb-0">
                    <div class="header-row flex items-center justify-between mb-4">
                        <div class="post-time text-xs text-gray-500 font-medium">{{ post.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        <div class="btn-group flex gap-2">
                            <button class="btn-secondary px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-indigo-600 text-white hover:bg-indigo-700 hover:-translate-y-0.5 edit-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="{{ post.id }}">编辑</button>
                            <button class="btn-danger px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-500 text-white hover:bg-red-600 hover:-translate-y-0.5 delete-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="{{ post.id }}">删除</button>
                        </div>
                    </div>
                    <div class="post-content text-base leading-relaxed text-gray-800 mb-6 break-words markdown-content" id="content-{{ post.id }}">{{ post.rendered_content|safe }}</div>
                    <!-- 标签容器 -->
                    <div class="tags-container flex flex-wrap gap-2 mt-4">
                        {% for tag in post.tags %}
                        <a href="{{ url_for('index', tag=tag) }}"
                            class="group relative inline-flex items-center px-3 py-1.5 text-xs font-medium no-underline rounded-full transition-all duration-300 ease-out
                                   {% if current_tag == tag %}
                                     bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/25 scale-105
                                   {% else %}
                                     bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 border border-indigo-200 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 hover:border-indigo-300 hover:scale-105 hover:shadow-md hover:shadow-indigo-500/10 hover:-translate-y-0.5
                                   {% endif %}">
                            <!-- 标签文本 -->
                            <span class="relative z-10">{{ tag }}</span>

                            <!-- 激活状态指示器 -->
                            {% if current_tag == tag %}
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                            {% endif %}

                            <!-- 悬停背景效果 -->
                            {% if current_tag != tag %}
                            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                            {% endif %}
                        </a>
                        {% endfor %}

                        <!-- 无标签提示 -->
                        {% if not post.tags %}
                        <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-400 bg-gray-100 rounded-full border border-gray-200">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            无标签
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页导航 -->
        {% if pagination.pages > 1 %}
        <nav class="pagination flex justify-center items-center gap-2 mt-12 mb-8">
            {% set url_params = {} %}
            {% if request.args.get('q') %}
                {% set _ = url_params.update({'q': request.args.get('q')}) %}
            {% endif %}
            {% if current_tag %}
                {% set _ = url_params.update({'tag': current_tag}) %}
            {% endif %}

            <a href="{{ url_for('index', page=pagination.prev_num, **url_params) if pagination.has_prev else '#' }}"
                class="flex items-center justify-center w-10 h-10 rounded-xl no-underline font-medium transition-all duration-300 {% if not pagination.has_prev %}text-gray-400 cursor-not-allowed opacity-50{% else %}text-gray-600 border border-gray-200 bg-white hover:bg-indigo-600 hover:text-white hover:shadow-sm{% endif %}">&laquo;</a>
            {% for page in pagination.iter_pages() %}
            {% if page %}
            <a href="{{ url_for('index', page=page, **url_params) }}" class="flex items-center justify-center w-10 h-10 rounded-xl no-underline font-medium transition-all duration-300 {% if page == pagination.page %}bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0{% else %}text-gray-600 border border-gray-200 bg-white hover:bg-indigo-600 hover:text-white hover:shadow-sm{% endif %}">{{ page }}</a>
            {% else %}
            <span class="flex items-center justify-center w-10 h-10 rounded-xl text-gray-400 cursor-not-allowed opacity-50">...</span>
            {% endif %}
            {% endfor %}
            <a href="{{ url_for('index', page=pagination.next_num, **url_params) if pagination.has_next else '#' }}"
                class="flex items-center justify-center w-10 h-10 rounded-xl no-underline font-medium transition-all duration-300 {% if not pagination.has_next %}text-gray-400 cursor-not-allowed opacity-50{% else %}text-gray-600 border border-gray-200 bg-white hover:bg-indigo-600 hover:text-white hover:shadow-sm{% endif %}">&raquo;</a>
        </nav>
        {% endif %}
    </div> <!-- 主容器结束 -->

    <!-- 删除确认对话框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="deleteConfirmModal">
        <div class="bg-white my-[10%] mx-auto rounded-2xl w-[95%] max-w-lg shadow-2xl animate-slide-in overflow-hidden">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-800 m-0">确认删除</h5>
                <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body p-8">
                <p class="text-gray-700 text-base">确定要删除这条微博吗？</p>
            </div>
            <div class="modal-footer px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4">
                <button type="button" class="px-4 py-2 text-sm font-semibold bg-indigo-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-indigo-700" data-dismiss="modal">取消</button>
                <form id="deletePostForm" method="POST" class="inline">
                    <button type="submit" class="btn-danger px-4 py-2 text-sm font-semibold bg-red-500 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-red-600">删除</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        window.API_URLS = {
            createPost: '{{ url_for("create_post") }}',
            updatePost: '{{ url_for("update_post", post_id=0) }}'
        };
        const searchQuery = "{{ search_query or '' }}";
        
        // 检查URL参数，如果有openPost=1则自动打开发布模态框
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('openPost') === '1') {
                // 延迟一点时间确保页面完全加载和main.js初始化完成
                setTimeout(function() {
                    const postButton = document.getElementById('postButton');
                    if (postButton) {
                        postButton.click();
                    }
                    // 清理URL参数，避免刷新页面时重复打开
                    const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname;
                    window.history.replaceState({path: newUrl}, '', newUrl);
                }, 100);
            }
        });
    </script>
    <script src="/static/js/search.js"></script>
    <script src="/static/js/tags.js"></script>
    <script src="/static/js/main.js"></script>
</body>

</html>