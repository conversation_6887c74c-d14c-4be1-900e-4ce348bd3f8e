<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签列表 - 我的微博</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- 引入 Tailwind CSS -->
    <link href="/static/css/tailwind/tailwind.min.css" rel="stylesheet">
    <link href="/static/css/tailwind-custom.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 主容器 -->
    <div class="max-w-6xl mx-auto px-8 pt-20 pb-12 md:px-4 md:pt-16">
        <!-- 导航栏 -->
        {% set active_page = 'tags' %}
        {% include '_navbar.html' %}

        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4 md:text-3xl">
                标签列表
            </h1>
            <p class="text-gray-600 text-lg md:text-base">
                探索所有标签，发现更多精彩内容
            </p>
        </div>

        <!-- 标签网格 -->
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4">
            <!-- 无标签选项 -->
            <a href="{{ url_for('index', tag='no_tags') }}"
               class="group relative bg-white rounded-xl p-3 border border-red-200 transition-all duration-300 ease-out hover:shadow-lg hover:shadow-red-500/10 hover:-translate-y-1 hover:border-red-300 no-underline">
                <!-- 背景装饰 -->
                <div class="absolute inset-0 bg-gradient-to-br from-red-50 to-pink-50 rounded-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300"></div>

                <!-- 内容 -->
                <div class="relative z-10 flex items-center justify-between min-h-[48px]">
                    <!-- 标签名 -->
                    <h3 class="text-sm font-semibold text-red-600 group-hover:text-red-700 transition-colors duration-300 truncate pr-2 flex-1 md:text-xs">
                        无标签
                    </h3>

                    <!-- 计数 -->
                    <span class="inline-flex items-center justify-center px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-full group-hover:bg-red-600 transition-colors duration-300 min-w-[24px] flex-shrink-0">
                        {{ no_tags_count }}
                    </span>
                </div>

                <!-- 悬停效果 -->
                <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-pink-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>

            <!-- 普通标签 -->
            {% for tag_count in tag_counts %}
            <a href="{{ url_for('index', tag=tag_count.tag) }}"
               class="group relative bg-white rounded-xl p-3 border border-gray-200 transition-all duration-300 ease-out hover:shadow-lg hover:shadow-indigo-500/10 hover:-translate-y-1 hover:border-indigo-300 no-underline">
                <!-- 背景装饰 -->
                <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>

                <!-- 内容 -->
                <div class="relative z-10 flex items-center justify-between min-h-[48px]">
                    <!-- 标签名 -->
                    <h3 class="text-sm font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300 truncate pr-2 flex-1 md:text-xs" title="{{ tag_count.tag }}">
                        {{ tag_count.tag }}
                    </h3>

                    <!-- 计数 -->
                    <span class="inline-flex items-center justify-center px-2 py-1 bg-indigo-500 text-white text-xs font-bold rounded-full group-hover:bg-indigo-600 transition-colors duration-300 min-w-[24px] flex-shrink-0">
                        {{ tag_count.count }}
                    </span>
                </div>

                <!-- 悬停效果 -->
                <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
            {% endfor %}
        </div>

        <!-- 空状态 -->
        {% if not tag_counts and no_tags_count == 0 %}
        <div class="text-center py-20">
            <h3 class="text-2xl font-semibold text-gray-600 mb-4">暂无标签</h3>
            <p class="text-gray-500 mb-8 text-lg">还没有任何标签，快去发布第一条微博吧！</p>
            <button id="postButton" class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                发布微博
            </button>
        </div>
        {% endif %}
    </div>

    <script>
        // 发布按钮点击事件 - 跳转到主页并打开发布模态框
        document.addEventListener('DOMContentLoaded', function() {
            const postButtons = document.querySelectorAll('#postButton');
            postButtons.forEach(button => {
                if (button) {
                    button.addEventListener('click', function() {
                        // 跳转到主页并添加参数来指示打开发布模态框
                        window.location.href = '{{ url_for("index") }}?openPost=1';
                    });
                }
            });

            // 添加标签卡片的交互效果
            const tagCards = document.querySelectorAll('a[href*="tag="]');
            tagCards.forEach(card => {
                // 添加键盘导航支持
                card.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });

                // 添加焦点样式
                card.addEventListener('focus', function() {
                    this.classList.add('ring-4', 'ring-indigo-500/20');
                });

                card.addEventListener('blur', function() {
                    this.classList.remove('ring-4', 'ring-indigo-500/20');
                });
            });
        });
    </script>
    <script src="/static/js/search.js"></script>
</body>
</html>